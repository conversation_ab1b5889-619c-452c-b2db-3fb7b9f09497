Executive Summary
In the world of digital libraries and bookstores, finding the perfect book for each reader has become hard and essential at the same time. This project introduces a book recommendation system that utilizes data processing methods, including traditional sorting and filtering algorithms. 

The system allows users to filter books on the basis of year, genre, and author, and then sort the results by average ratings of other users using sorting algorithms such as Bubble Sort, Merge Sort, and Shell Sort. A list of dictionary data structures was used to store the data from the dataset of books. Since it would be easier to access the values using the keys. The sorting algorithms were compared using static and dynamic analysis. The time module of Python was utilized for the dynamic analysis. Whereas, Worst Case Time Complexity was calculated for static analysis. 

After the analysis, it was found that for medium datasets, shell sort is the better choice. Merge sort was a bit slower than Shell Sort. Bubble Sort was slower than other algorithms. Merge Sort can be a better algorithm than shell sort for large datasets as seen at Static Analysis.
Introduction
This technical report showcases a Book Recommendation System that helps readers find books matching their tastes. While this system focuses on books, you can apply its methods and structure to suggest other digital content like music, podcasts, or courses. These days, we have so much digital stuff to choose from that picking the right book out of thousands can be too much. That's why content-based recommendation systems are key tools to point users toward choices that matter to them personally.

The book suggestion tool provides the highest-rated books that match what the reader likes in terms of genre, author, and when the book came out. To do this, a filter first goes through the user's choices cutting out books that don't fit. Then, the remaining books are put in order using three basic sorting methods: Bubble Sort, Merge Sort, and Shell Sort all built from scratch.

Each sorting method is tested to see how fast it works by calculating. This helps figure out which method does the best job with different numbers of books. The aim is to build a quick, clear, and flexible system that can grow to handle bigger book collections, like those from Open Library or Google Books.







Problem Statement
The number of books online and on bookstores keeps growing. Because of this it is tough for readers to pick which book to read next. A simple decision can take a long time and often leave people feeling frustrated because of the large number of books. It is hard for readers to find books that match what they like. This means we need intelligent systems to make book selection easier.
The problems are:
Finding the Highest Rated Books by Sorting.
Comparing Various Sorting Algorithms to Identify the Fastest One.
Searching for Books Written by a Specific Author.
Looking for Books in Specific Genre.
The types of books people like shape their reading choices. A system that can sort and show books by category helps readers find new titles they might enjoy making it easier to discover books and keep people interested.
Theory
The dataset used is from Goodreads. It consists of 350 rows and 5 columns. The columns in the dataset are:
Name: name of the book
Author: author of the book
Rating: Average rating given by other users to the book
Year: Year of publication
Genre: Category of the book
Implementation
For recommending the best book for the user. Author, Rating, Year and Genre column is used. The recommendation is based on filtration and sorting; therefore, the data is initially filtered by author, genre, and year. 
Data Structure
The data from the CSV file is stored in a List of Dictionary data structure. Each column and an individual value are stored in a dictionary as a key-value pair for easier access to the values. The dictionary consists of keys such as name, author, year, rating, and genre. All the dictionaries are stored in a list which looks like:

Fig 1: List of Dictionary Data Structure in Book Recommendation System
Filtering
The following steps were performed for filtering:

Fig 2: Importing modules and Libraries, reading the dataset and converting dataset to list of dictionaries
Step 1: Get the libraries you need, like pandas, to work with data.
Step 2: Read the data from the CSV file and put it into a pandas DataFrame.
Step 3: Change the DataFrame into a list of dictionaries to make it easier to work with.

Fig 3: Filtering the data based on Genre Fiction
Step 4: Filter the List based on the Author and Genre of Books
The filtering algorithm allows the user to narrow down books by:
Year: Filters books released in a specified year.
Genre: Filters by specific genre.
Author: Filters by author's name.

Algorithms
After filtering algorithms such as Bubble Sort, Merge Sort, and Shell Sort are then applied to the filtered data to sort the data based on average rating given by other readers. For finding the best and fastest algorithms multiple algorithms were used such as:
Bubble Sort:	
Bubble Sort is a sorting Algorithm that works by continuously comparing adjacent elements and swapping them. The swapping process is done until the array is sorted. Bubble Sort is not good for big datasets compared to other sorting algorithms (Astrachan, 2003).

Fig 4: Bubble Sorting based on Average user Rating

In the above Code Snippet of Bubble Sort, the algorithm uses two nested loops. The outer loop (i) controls the number of total passes over the list. The inner loop (j) compares each pair of adjacent ratings and swaps them if they are in the wrong order.

A key behavior of Bubble Sort is that with each complete pass through the inner loop, the largest unsorted element "bubbles" to its correct position at the end of the list. This is why the inner loop only needs to go up to n - i - 1, reducing unnecessary comparisons for already sorted elements at the end.
Merge Sort
Merge Sort splits things up and puts them back together. It's a method that breaks down a list into smaller parts. It keeps doing this until each part has just one item. Then, it combines these sublists back into one big sorted list (Lobo and Kuwelkar, 2020).


Fig 5: Merge Sorting based on Average user Rating
The merge_sort function begins by looking at the input list (data) to see if it has one or zero items. If it does, we consider it already sorted, and the function gives back the list right away. If not, it finds the middle of the list and calls itself again on the left and right parts. This splitting keeps going until all smaller lists have just one item. When this happens for all the smaller lists, the merge function steps in and starts putting the single items back together into a sorted list.

The merge function combines two sorted sublists—left and right—into one sorted list. It uses two pointers, i and j, to keep track of positions in each sublist. The function compares the current items in both sublists based on their 'rating' value. The item with the higher rating goes into the result list first, as the aim is to sort from highest to lowest. This keeps going until one of the sublists runs out of items. Then, any leftover items from the other sublist (which are already in order) are added to the end of the result. The end product is a sorted list of books by rating. Merge Sort always has an O(n log n) time complexity, which makes it dependable and fast for big datasets. But it does need extra space because it makes new lists during the merging process.
Shell Sort
Shell Sort is an optimized version of Insertion Sort. Instead of comparing and swapping adjacent elements one at a time, it allows the comparison of elements that are far apart (Sedgewick, 1996).

Fig 6: Shell Sorting based on Average user Rating
Firstly, the gap is set to half the list size (n//2). In each iteration, elements that are gap distance apart are compared. If an element with lower rating is found in front of the higher rated element then they are swapped. The gap is gradually reduced by halving it which is done by gap //= 2 and the same swapping process takes place. Eventually, when the gap becomes 1, the algorithm works just like insertion sort by checking and swapping the elements of nearly sorted list. So, If the gaps are set correctly then this sorting mechanism is more efficient then insertion sort.

Time Complexity
Dynamic Analysis:

Fig 8: Dynamic Time Calculation for bubble sort Algorithm

Fig 9: Dynamic Time Calculation for merge sort Algorithm

Fig 10: Dynamic Time Calculation for bubble sort Algorithm
Using the time module, Runtime was calculated for each sorting algorithm to sort the entire dataset based on rating. Bubble Sort took 0.0175s, Merge Sort took 0.002s, and Shell Sort took 0.0019s. 
This shows that bubble sort algorithm was the slowest algorithm because it keeps checking all the elements and swapping them. This can be efficient for small datas but for big data it is kind of slow. The merge sort algorithm took significantly less time than bubble sort algorithm. merge sort and shell sort where almost similar where shell sort was faster. It is the case probably because the Shell sort algorithm was  in-place so no new variables were declared and took less memory and Merge sort algorithm was out-of-place where a new variable results was used.
Static Analysis:
 
Since the Bubble Sort algorithm has 2 for loops which runs n times each. Combining them together,
T(n) = O(n) x O(n)
T(n) = O(n²)
The Worst Case Time Complexity for Bubble Sort is  O(n²).


Since we do merge sort on each halves it takes T(n/2) time each. And since we are doing the operation on each item in the list the while loop runs n times. Putting them together the time complexity is,
T(n) = T(n/2)+T(n/2)+O(n)
T(n) = 2T(n/2)+O(n)
T(n) = O(n log(n))
Therefore, the Worst Case Time Complexity for Merge Sort is O(n log(n)).

So, for the worst case to happen, the gap must be 1. When that happens, the algorithm behaves like insertion sort, which has a worst-case time complexity of O(n²). When the gap is large like n//2 or n//4, the elements to be compared are far so only few shifts should occur. But when. The gap is 1. The algorithm compares the elements next to each other, just like insertion sort. Also, if list is highly unsorted, every element needs to be sorted which takes n x n operations to happen. So when the gap is 1, the Worst Case Time Complexity of shell sort is O(n²).
Assertion Table

Assertion
Steps
Output
Assert: dataframe is not empty

Assert: books is a list

Assert: books is not empty

Working
Assert: books is a list

Assert: book is a dictionary

Assert: year is an integer

Assert: genre is a string

Assert: author is a string

Assert: filtered is list

Working
Assert: data is a list

Assert: book is a dictionary

Working
Assert: data is a list

Assert: book is a dictionary

Assert: rating is a integer or float

Working
Assert: data is a list

Assert: book is a dictionary

Assert: The list is sorted in descending order.

Working


Conclusion
In conclusion, the book discussed a book recommendation system based on users' preferences. Books were recommended based on filtering based on specific year, author, and genre. The top books were found based on sorting the average rating in descending order of the filtered data using sorting algorithms such as bubble sort, merge sort, and shell sort. According to Dynamic Analysis, it was found that Shell Sort was the faster sorting algorithm.  Although merge sort is supposed to be the faster one based on the Static analysis, It was found that for medium-sized datasets, Shell sort was a better choice. 

