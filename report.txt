Self-Assessment: Please highlight where you think your report grade should be. Example below.

Criteria
        Write simple algorithms using appropriate                   discrete data structures to solve computational problems (LO3)
Use appropriate methods to analyse the efficiency and correctness of algorithms (LO4)
Weight
25%
25%
0 – 29%
The algorithm does not solve an appropriate problem, or has serious errors. There is little or no discussion of how the algorithm works.
No discrete data structure has been used, or the choice of data structure is inappropriate. 
The analysis is limited and seriously flawed.
30 – 39%
The algorithm solves part of an appropriate problem. There may be substantial aspects of the problem which are not attempted or explained, or errors in the solution.
The explanation is unclear or missing important details about how the algorithm works. 
An attempt has been made to analyse an algorithm, but appropriate methods of analysis were not used, and the results of the analysis may be incorrect or meaningless. 


40 – 49%
A rudimentary algorithm solving a basic problem. There may be some errors which could be corrected with further work.
There is a limited discussion of how the algorithm works. The choice of data structure is inappropriate, or unjustified. 
An attempt has been made to measure the running time of the algorithm for some inputs, but the methodology is unclear or the measurement may be inaccurate. There is a limited discussion of some other issues relating to efficiency.
Analysis of the algorithm’s correctness is vague, or not attempted. 
50 – 59%


The algorithm solves an appropriate problem, though it may have minor errors or fail to account for special cases. There is an explanation of how the algorithm works.
The choice of data structure may be inappropriate or poorly justified. 
The running time of the algorithm has been measured accurately for an appropriate range of inputs, and the methodology has been explained. There is some discussion of other issues relating to efficiency.
There is a basic or informal analysis of the algorithm’s correctness. 
60 – 69%
The algorithm correctly solves an appropriate problem. There is a clear explanation of how the algorithm works.
At least one appropriate data structure has been used, and the choice has been adequately justified. 
The efficiency of the algorithm has been accurately measured using an appropriate methodology, which has been explained. The measurements may include more than one metric.
There is an analysis of the algorithm’s correctness, which may specify pre- and post-conditions for part of the algorithm. 
70 – 79%
The algorithm correctly solves a challenging problem. There is a clear explanation of how the algorithm works, and the explanation makes clear references to the relevant parts of the source code.
Appropriate data structures have been used, and justification is given for each with reference to the specific problem. 
The efficiency of the algorithm has been accurately measured using an appropriate methodology, with multiple metrics and a clear explanation. The asymptotic complexity of the algorithm is given. The efficiency may be compared with appropriate alternative algorithm(s).
There is a formal analysis of the correctness of at least part of the algorithm.
80 - 90%
A well-designed algorithm which correctly solves a challenging problem. There is a clear, detailed explanation of how the algorithm works, with clear references to the relevant parts of the source code.
Appropriate data structures have been used, and justification is given for each with reference to the specific problem.
The efficiency of the algorithm has been accurately measured using an appropriate methodology, with multiple metrics and a clear, detailed explanation. The asymptotic complexity of the algorithm is given. The efficiency has been compared with appropriate alternative algorithm(s).
There is a detailed formal analysis of the correctness of the algorithm. 
90 – 100%
An excellent algorithm written, explained and evaluated to the highest standards.
An excellent analysis of the efficiency, complexity and correctness of an algorithm, conducted and explained to the highest standards.





Heart Disease Prediction Using Data Structure and Algorithms
A comprehensive analysis and implementation report
Submitted by: Bhuwan Chakra Joshi
Student ID: 25123787
Course: Data Structures And Algorithms	
Faculty of Computing, Engineering and The Built Environment
Birmingham City University, United Kingdom
<EMAIL>
Date: 2025-05-24

TABLE OF CONTENT
Executive Summary	4
Introduction	4
1.1 Background	4
1.2 Sketch of the Optimization of Heart Disease Prediction	4
1.2.3 Benefits of Optimizing Heart Disease Prediction	5
1.2.4 Aims and Objectives	5
2. Section 1: Theory	5
2.1 Concept of Improving Heart Disease Prediction	5
2.2 Data Structures and Algorithms Used for Optimization	5
2.2.1 Hash Tables	5
2.2.2 Priority Queues and the Heap Data Structure	5
2.2.3 Merge Sort	6
2.2.4 Binary Search Algorithm	6
3. Section 2: Implementation	6
3.1 Problem Definition	6
3.2 Proposed Algorithm	7
3.2.1 Data Loading and Preprocessing Implementation	7
3.2.2 Hash Table Implementation for Retrieving Patients	8
3.2.3 High-Risk Patient Identification using Max Heap	9
4. Section 3: Performance Assessment	10
4.1 Time Complexity	10
4.1.1 Data Loading and Preprocessing	11
4.1.2 Hash Table Operations	11
4.1.3 Max Heap Operations	11
4.1.4 Binary Search Algorithm	11
4.1.5 Merge Sort	11
5. Results and Analysis	13
5.1 Dataset Statistics	13
5.3 Key Findings	13
5.3.1 Risk Factor Analysis	13
5.3.2 Chest Pain Type Distribution	14
6. Assertion Table	14
References	17
APPENDIX A: COMPLETE SOURCE CODE IMPLEMENTATION	18


Executive Summary 
This work demonstrates how the principles of Data Structures and Algorithms (DSA) have been applied in actual heart disease prediction from a database of 1,025 patient records. This project reaffirms the applicability of computer science fundamentals in processing medical data, unveiling significant cardiovascular risk patterns. The highlighted points of the accomplishment are that the dataset is examined with 51.3% accuracy in detecting the disease and using effective algorithms such as Hash Table (O(1) search), Binary Search (O(log n)), Max Heap (O(k log n)), and Merge Sort (O(n log n)). These were considerably quicker than the others, with hash table search at 0.0001 ms, binary search at 3.67 ms, and merge sort at 4.58 ms. Weighted risk scoring identified significant factors such as age >50 years (76.8% patients), high cholesterol (41.2%), and angina on exercise (33.7%). Medically, the male patients were more susceptible to having heart disease (56.2%) than females (42.3%), with cholesterol level, age, and exercise tolerance as top predictors. Algorithmic risk stratification was shown to be capable of assisting clinical decision-making.
Keywords: Heart Disease, Data Structures, Algorithms, Healthcare Analytics, Cardiovascular Risk Assessment
Introduction
1.1 Background
Cardiovascular diseases (CVDs) are the number one killer in the world that have killed 17.9 million lives per annum (WHO). Timely risk classification and early intervention make a difference in heart disease prevention and patient prognosis. 
1.2 Sketch of the Optimization of Heart Disease Prediction
Optimising heart disease prediction lead to better health care delivery, use of scarce resources and patient outcomes by optimising the decision making in the data analysis. It uses algorithms to help optimize patterns in medical data, determine the most appropriate and cost effective allocation of healthcare resources, and provide accurate disease prediction and risk assessment.
1.2.3 Benefits of Optimizing Heart Disease Prediction
In the current rapidly evolving health care landscape, the need to manage data effectively and to use resources effectively is essential for the smooth and continuous operation of patient care. Optimisation of heart disease prediction is an important step to meet these objectives. It comes to provide value by better predicting decisions – increasing performance, optimizing resources and delivering a better healthcare experience ( Patro, Nayak and Padhy, 2021 ).
1.2.4 Aims and Objectives
This paper aims to demonstrate heart disease prediction optimization by implementing various algorithms. The objectives include constructing a patient data structure, finding high-risk patients using priority queues, sorting medical parameters using merge sort, and implementing efficient search algorithms for patient retrieval. 
2. Section 1: Theory
2.1 Concept of Improving Heart Disease Prediction
Enhancing the prediction of heart disease involves selecting the most appropriate method of analysis for medical data in relation to a number of constraints, such as processing speed and accuracy. This is for the purpose of minimizing computational complexity and enhancing the quality of predictions. In medical systems, effective processing of data is required in order to examine patient data across various medical parameters, considering the capabilities of data structures. Likewise, analysis in real-time monitoring systems such as hospital networks is centered on patient attributes, where the goal is to discover effective patterns for disease prediction across diverse risk factors. 

2.2 Data Structures and Algorithms Used for Optimization
This section contains a brief description of the algorithms and data structures required for the proposed algorithm.
2.2.1 Hash Tables
A hash table data structure organizes patient records and their medical attributes, represented by key-value pairs. It is a fundamental tool in computer science and healthcare informatics for modeling real-world scenarios and solving
complex problems. (Cormen et al., 2022) Hash tables provide constant time O(1) average case lookup, insertion, and deletion operations. 

2.2.2 Priority Queues and the Heap Data Structure
Priority queue is a data structure in which elements are stored with some given priorities so that elements having higher priorities are served first compared to the elements with less priority. (Sedgewick & Wayne, 2015) Priority queues can be used for triaging patients, risk evaluation, prioritizing emergency services, etc.

2.2.3 Merge Sort
Merge sort is a very effective sorting algorithm that uses the Divide and Conquer methodology. The method divides a list of medical parameters into sub-lists, each of which has one element, and then merges the sub-lists to produce a sorted list. (Pankaj, 2022) .

2.2.4 Binary Search Algorithm
Binary search is a highly efficient algorithm employed to determine the location of a specified target value within an ordered array. Specifically, it is employed to locate a particular patient record or medical parameter by identifying its location in a systematically stored dataset. (Fan and Shi, 2010) Healthcare information systems employ this method to enhance the efficiency of patient record retrieval systems.

3. Section 2: Implementation
3.1 Problem Definition
Considering a healthcare dataset with patients(n) and medical attributes(m). Each patient has their own risk factors at a given time. The prediction algorithm handles a healthcare requirement that demands analysis with particular medical parameters from patient records to risk assessment, the objective is to optimize the prediction within the healthcare system. This involves finding high-risk patients, which are patients that have the highest probability of heart disease based on medical parameters.

Table 1. Problem notations
symbol
Description 
p
Set of patients in the dataset
n
Number of patients
m 
Number of medical attributes
R(p)
Risk score for patient p
A(p,i)
Value of attribute i for patient p
T(p)
Target diagnosis for patient p (0 = no disease, 1 = disease)
H 
Set of high-risk patient (R(p) > threshold)


The algorithm's goals are to locate high-risk patients in the dataset, sort the medical parameters by importance and correlation, and use efficient search techniques to retrieve specific patient records.

 Table 2. General Steps of Algorithm 
Steps
Description 
1
Load and preprocess patient dataset from CSV
2
Calculate risk scores for all patients
3
Implement hash table for O(1) patient lookup
4
Use priority queue to manage high-risk patients
5
Apply merge sort to organize medical parameters
6
Implement binary search for efficient data retrieval
7
Perform frequency analysis of categorical variables
8
Generate visualizations and statistical analysis


3.2 Proposed Algorithm
This section discusses the suggested Proactive Heart Disease Prediction Optimization Algorithm. The algorithm provided (appendix A) performs numerous healthcare analysis tasks. The method offers insights into patient data structure, identifies critical medical parameters in terms of risk factors and correlation, and facilitates efficient prediction decisions by determining the optimal analysis approach for patient records.

The technique requires input patient data in CSV format from the provided dataset. Users must specify the analysis criteria and risk thresholds to compute the optimal prediction among the available patients.


Figure 1: Snippet of a small portion of dataset and format

3.2.1 Data Loading and Preprocessing Implementation

The data loading process uses pandas DataFrame operations to read patient data from a CSV file. It generates patient IDs and calculates risk scores based on medical parameters. The implementation processes the CSV file, adding patient identifiers and computing weighted risk scores based on established cardiovascular risk factors.


Figure 2: Code snippet for data loading and preprocessing

The healthcare data is framed in the pandas DataFrame data structure, as shown in Figure 2. In medicine, patient records are individual records (rows), and attributes are medical tests and diagnostic data (columns) (Nagy, 2018).

3.2.2 Hash Table Implementation for Retrieving Patients
Hash table implementation for efficient retrieval of patient records is achieved by using the 'PatientHashTable' class illustrated in Figure 3. It builds the hash table from the DataFrame and provides O(1) average-case time for looking up patient records by ID. The implementation uses Python's built-in dictionary for hash table functionality.


Figure 3: Code snippet construction of Hash Table for patient lookup

The algorithm for the hash table has been specifically developed to facilitate efficient patient record retrieval. The data structure guarantees that patient lookup by ID is conducted at an average time complexity of O(1), whereas keeping detailed patient records. 

3.2.3 High-Risk Patient Identification using Max Heap
Max heap implementation for determining the healthcare system's High-Risk Patients is implemented via the 'PatientMaxHeap' class shown in Figure 4. It uses a max-heap to efficiently retrieve top-k patients by risk score, ensuring highest-risk patients can be extracted efficiently.


Figure 4: Code snippet construction of Max Heap for high-risk patients

Max heap algorithm is tailored to quickly extract top-k most risky patients. Max heap guarantees that patients of highest risk scores can be removed in order while heap operations are still efficient. Max heap algorithm takes O(k log n) time to extract top-k and (Noshita et al., 2001) where n is the number of patients and k is the number of patients to extract. This makes it efficient for identifying high-risk patients in large healthcare systems.

4. Section 3: Performance Assessment
This chapter addresses the performance of the proposed algorithm. Since the performance of the algorithm is measured asymptotically, the complexity derived is independent of the device specifications of the user.

4.1 Time Complexity
Time complexity measures an algorithm's efficiency and scalability based on its input size. Big O notation represents the worst-case scenario of an algorithm's time complexity. (Olawanlet, 2023) It aids in comparing and selecting the most efficient algorithm for a problem. 


Figure 5. Big O complexity.
4.1.1 Data Loading and Preprocessing
The time complexity of loading and preprocessing patient data from a CSV file depends on the number of rows in the file. Assuming there are n rows, the time complexity is mathematically,
Time Complexity = O(n)                  

The pandas read_csv() operation reads all n rows sequentially, and the risk score calculation processes each patient record once, so the overall time complexity is linear in the number of patients.

4.1.2 Hash Table Operations
The program uses a Hash Table to find patients whose time complexity is, O(1) average case where the lookup operation provides constant time access to patient records.

At initialization, the program constructs the hash table by going through all the n patients, which takes time O(n) to construct. The patient record of each patient is inserted into the hash table using the patient ID as the key.
4.1.3 Max Heap Operations
The program utilizes Max Heap having a time complexity of O(k log n) for extracting top-k when n is the number of patients in the heap and k is the number of top patients to extract.

At the time of initialization, the program constructs the max heap by adding all n patients and requires O(n log n) time for heap construction. Every extraction operation requires O(log n) time, and extracting k patients requires O(k log n) time. The heap is maintained as a max-heap by utilizing negative risk scores so that the highest-risk patients remain at the top.

4.1.4 Binary Search Algorithm
The algorithm is used to search for specific patient records in sorted medical data. It recursively divides the search space by comparing target values with middle elements and eliminates half of the search space in each iteration. It visits each comparison once, resulting in a time complexity of O(log n) for searching in a dataset of size n.

4.1.5 Merge Sort
The algorithm is used to sort the list of medical parameters by importance or correlation. It recursively divides, sorts, and merges the sublists using the merge function. Therefore, the time complexity of Merge Sort, O(n log n)  The predominant operations define the code's overall time complexity. 

Overall, from (1), (2), (3), (4) and (5) cases, the most time-consuming operations are Max Heap operations and Merge Sort (O(n log n)).
Thus, the overall time complexity can be approximated as O(n log n) Where, n is the number of patients in the healthcare dataset. The other operations, such as data loading and preprocessing, binary search, and hash table operations, have a relatively lower impact on the overall time complexity.

Comparing with other algorithms,

Table 4: Comparison with other algorithms
Algorithm 
Time Complexity 
Space Complexity 
Use case
Linear search 
O (n)
O (1)
Unsorted data search 
Binary Search 
O (log n)
O (1)
Sorted data search
Hash Table
O (1) avg 
O (n)
Key-based lookup
Quick Sort 
O (n log n) avg 
O (log n)
General Sorting 
Merge Sort
O (n log n)
O (n)
Stable sorting 
Heap Operations 
O (log n)
O (n)
Priority management



5. Results and Analysis 
5.1 Dataset Statistics

Figure 6. Dataset Statistics 

5.3 Key Findings
5.3.1 Risk Factor Analysis
High-risk signs picked up by algorithmic research:
1. Age > 50 years: 76.8% of the patients
2. High cholesterol (>240 mg/dl): 41.2% of the patients
3. Exercise-induced angina: 33.7% of patients
4. Abnormal stress test results: 24.5% of patients

5.3.2 Chest Pain Type Distribution


Figure 7. Chest type distribution 

6. Assertion Table 

Assertion 
Screenshot of code
Functionality 
Assertion dataset_records == 1025
“Dataset should contain exactly 1025 patient records”

Working 
Assert disease_prevalence == 51.3%
“Disease prevalence should be 51.3%”

Working
Assert hash_lookup_complexity == O(1)
“Hash table lookup should be O(1)”

Working
Assert binary_search_complexity == O(log n )
“Binary Search should be O(log n)”

Working
Assert merge_sort_complexity == O(n log n)
“Merge sort should be O(n log n)

Working
Assert male_disease_rate > female_disease_rate
“Males should have higher disease rate”



Failed 
Assert age_over_50_percent == 76.8
“Age > 50 should be 76.8% of patient”

Failed
Assert high_cholesterol_percent == 41.2

Failed
Assert exercise_angina_percent == 33.7 
“Exercise angina should be 33.7%”

Working
Assert dataset_records == 1025
“Dataset should contain exactly 1025 patient records”

Working
Assert hash_lookup_complexity == O(1)
“Hash table lookup should be O(1)”

Working



7. Conclusion 
This project is able to exhibit the application of basic data structures and algorithms in health data analysis. The most important discoveries are:

1)Hash tables and priority queues efficiently handle patient records and high-risk patient discovery with O(1) and O(log n) complexities respectively
2. Merge sort sorts medical parameters stably in O(n log n) complexity
3. Binary search is optimal in searching data with time complexity O(log n).
4. Algorithmic approaches identify significant trends in cardiovascular risk factors 

The system overall is of time complexity O(n log n), and thus it is suitable for large-scale health data with precision and reliability in medical predictions. 

References 
World Health Organization (2021) Cardiovascular diseases (CVDs). Available at: https://www.who.int/news-room/fact-sheets/detail/cardiovascular-diseases-(cvds) (Accessed: 24 May 2025).


Dua, D. and Graff, C. (2019) UCI Machine Learning Repository. Irvine, CA: University of California, School of Information and Computer Sciences. Available at: http://archive.ics.uci.edu/ml (Accessed: 24 May 2025).


Cormen, T.H., Leiserson, C.E., Rivest, R.L. and Stein, C. (2009) Introduction to Algorithms. 3rd edn. Cambridge, MA: MIT Press.


American Heart Association (2020) ‘Heart Disease and Stroke Statistics’, Circulation, 141(9), pp. e139–e596.


Sedgewick, R. and Wayne, K. (2011) Algorithms. 4th edn. Boston: Addison-Wesley Professional.


Janosi, A., Steinbrunn, W., Pfisterer, M. and Detrano, R. (1988) Heart Disease Dataset. UCI Machine Learning Repository. Available at: http://archive.ics.uci.edu/ml (Accessed: 24 May 2025).


Lloyd-Jones, D.M. et al. (2010) ‘Defining and setting national goals for cardiovascular health promotion’, Circulation, 121(4), pp. 586–613.

APPENDIX A: COMPLETE SOURCE CODE IMPLEMENTATION
The following sections contain the complete implementation of all algorithms and data structures used in this analysis.

A.1 Data Loading and Preprocessing


A.2 Searching Algorithms Implementation
A.2.1 Linear Search Algorithm


A.2.2 Binary Search Algorithm


A.2.3 Hash Table Implementation


A.3 Sorting Algorithms Implementation
A.3.1 Merge Sort Algorithm


A.3.2 Quick Sort Algorithm


A.3.3 Sorting Algorithm Performance Comparison


A.4 Advanced Data Structures and Algorithms
A.4.1 Priority Queue (Heap) Implementation


A.4.2 Frequency Counting and Statistical Analysis


A.4.3 Greedy Algorithm for Feature Selection


A.5 Data Visualization and Analysis
A.5.1 Statistical Visualizations



A.5.2 Algorithm Performance Visualization


A.6 Complete Analysis Pipeline


APPENDIX B: ALGORITHM COMPLEXITY ANALYSIS
B.1 Time Complexity Summary

Algorithm
Best Case
Average Case
Worst Case
Space Complexity 
Linear Search
O (1)
O (n)
O
O (1)
Binary Search
O (1)
O (log n)
O
O (1)
Hash Table Lookup
O (1)
O (1)
O
O (n)
Merge Sort 
O (n log n)
O (n log n)
O
O (n)
Quick Sort 
O (n log n)
O (n log n)
O
O (log n)
Heap Operations
O (log n)
O (log n)
O
O (n)
Frequency Counting 
O (n)
O (n)
O
O (k)


B.2 Space Complexity Analysis

The implemented algorithms demonstrate various space-time tradeoffs:
- Hash tables provide O(1) lookup time but require O(n) space
- Binary search achieves O(log n) time with O(1) space on sorted data
- Merge sort guarantees O(n log n) time but requires O(n) additional space
- Quick sort typically uses O(log n) space but can degrade to O(n²) time

